<!DOCTYPE html>
<html lang="it">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Proxy42 Inc | IA Autonoma per il Business</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Work+Sans:wght@300;400;500;600;700;800&family=Plus+Jakarta+Sans:wght@300;400;500;600;700;800&display=swap"
        rel="stylesheet">
    <style>
        /* ------------------- */
        /* --- CSS Reset --- */
        /* ------------------- */
        *,
        *::before,
        *::after {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        html {
            scroll-behavior: smooth;
        }

        body {
            font-family: '"Work Sans"', sans-serif;
            background-color: #0B0B0F;
            color: #E0E0E0;
            line-height: 1.6;
            -webkit-font-smoothing: antialiased;
            padding-top: 80px;
            /* Space for fixed header */
        }

        img,
        picture,
        video,
        canvas,
        svg {
            display: block;
            max-width: 100%;
        }

        h1,
        h2,
        h3 {
            font-family: 'Plus Jakarta Sans', 'Work Sans', sans-serif;
            line-height: 1.2;
            color: #FFFFFF;

        }

        h1 {
            font-weight: bold;
        }

        h2,
        h3 {
            font-weight: 600;
        }

        /* ------------------- */
        /* --- Global Variables & Styles --- */
        /* ------------------- */
        :root {
            --accent-color: #4A55FF;
            --accent-hover: #6A71FF;
            --bg-color: #0B0B0F;
            --surface-color-transparent: rgba(26, 26, 34, 0.5);
            --surface-color: rgb(26, 26, 34);
            --border-color: #2D2D3A;
            --text-primary: #FFFFFF;
            --text-secondary: #b3b5bd;
            --container-width: 1320px;
        }

        .container {
            width: 90%;
            max-width: var(--container-width);
            margin: 0 auto;
            padding: 80px 0;
        }

        .container-small {
            width: 90%;
            max-width: var(--container-width);
            margin: 0 auto;
            padding: 10px 0;
        }

        .section-title {
            font-size: 48px;
            text-align: center;
            margin-bottom: 16px;
            max-width: 900px;
            margin-left: auto;
            margin-right: auto;
        }

        .section-subtitle {
            font-size: 18px;
            text-align: center;
            color: var(--text-secondary);
            max-width: 800px;
            margin: 0 auto 64px auto;
        }

        .cta-button {
                 display: inline-flex;
            align-items: center;
            gap: 6px;
            background: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-hover) 100%);
            color: var(--text-primary);
            padding: 16px 32px;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 20px rgba(74, 85, 255, 0.3);
            border: none;
            position: relative;
            overflow: hidden;
        }

        .cta-button::before {
           content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .cta-button:hover::before {
            left: 100%;
        }

        .cta-button:hover {
                 transform: translateY(-3px);
            box-shadow: 0 8px 30px rgba(74, 85, 255, 0.4);
        }

        .secondary-button {
                   display: inline-flex;
            align-items: center;
            gap: 8px;
            background-color: var(--surface-color-transparent);
            /* Changed from transparent */
            color: var(--text-primary);
            padding: 16px 32px;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 500;
            font-size: 16px;
            border: 2px solid var(--accent-color);
            /* Changed border to accent color */
            transition: all 0.3s ease;
            position: relative;
            opacity: 1;
            /* Ensured opacity is 1 */
        }



        .secondary-button:hover {
            background-color: var(--surface-color);
            opacity: 1;
            border-color: var(--accent-hover);
            /* Changed hover border to accent-hover for differentiation */
            transform: translateY(-2px);
        }

        /* ------------------- */
        /* --- Header & Nav --- */
        /* ------------------- */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 5%;
            background-color: rgba(11, 11, 15, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid transparent;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .header.scrolled {
            background-color: rgba(11, 11, 15, 0.98);
            border-bottom: 1px solid var(--border-color);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
        }

        .header-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            max-width: var(--container-width);
            margin: 0 auto;
        }

        .logo {
            font-size: 22px;
            font-weight: 700;
            color: var(--text-primary);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .logo:hover {
            color: var(--accent-color);
        }

        /* Desktop Navigation */
        .nav-desktop {
            display: flex;
            gap: 32px;
            align-items: center;
        }

        .nav-desktop a {
            color: var(--text-secondary);
            text-decoration: none;
            font-weight: 500;
            font-size: 14px;
            transition: color 0.3s ease;
        }

        .nav-desktop a:hover {
            color: var(--text-primary);
        }

        /* Mobile Hamburger Menu */
        .hamburger {
            display: none;
            flex-direction: column;
            cursor: pointer;
            padding: 10px;
            z-index: 1001;
            border-radius: 4px;
            transition: background-color 0.3s ease;
        }

        .hamburger:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .hamburger span {
            width: 24px;
            height: 2px;
            background-color: var(--text-primary);
            margin: 3px 0;
            transition: 0.3s;
            border-radius: 2px;
        }

        .hamburger.active span:nth-child(1) {
            transform: rotate(-45deg) translate(-5px, 6px);
        }

        .hamburger.active span:nth-child(2) {
            opacity: 0;
        }

        .hamburger.active span:nth-child(3) {
            transform: rotate(45deg) translate(-5px, -6px);
        }

        /* Mobile Navigation Menu */
        .nav-mobile {
            display: none;
            position: fixed;
            top: 0;
            right: -100%;
            width: 280px;
            height: 100vh;
            background-color: var(--surface-color);
            border-left: 1px solid var(--border-color);
            padding: 100px 32px 32px 32px;
            transition: right 0.3s ease;
            z-index: 999;
            box-shadow: -5px 0 15px rgba(0, 0, 0, 0.3);
        }

        .nav-mobile.active {
            right: 0;
        }

        .nav-mobile a {
            display: block;
            color: var(--text-primary);
            text-decoration: none;
            font-weight: 500;
            font-size: 18px;
            padding: 16px 0;
            border-bottom: 1px solid var(--border-color);
            transition: color 0.3s ease;
        }

        .nav-mobile a:hover {
            color: var(--accent-color);
        }

        .nav-mobile a:last-child {
            border-bottom: none;
        }

        /* Overlay */
        .nav-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 999;
        }

        .nav-overlay.active {
            display: block;
        }

        /* ------------------- */
        /* --- Partners Section --- */
        /* ------------------- */
        .partners-section {
            padding: 80px 0;
            background-color: var(--surface-color);
            border-top: 1px solid var(--border-color);
            border-bottom: 1px solid var(--border-color);
        }

        .partners-title {
            text-align: center;
            font-size: 16px;
            font-weight: 400;
            color: var(--text-secondary);
            margin-bottom: 24px;
            text-transform: uppercase;
            letter-spacing: 0.1em;
            font-family: '"Work Sans"', sans-serif;
            text-shadow: 0px 0px 5px rgb(70, 128, 255);
            /* Added font family */
        }

        .partners-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 40px;
            align-items: center;
            opacity: 1;
            /* Changed from 0.7 to 1 */
        }

        .partner-logo {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 15px;
            /* Adjusted padding */
            background-color: var(--surface-color);
            /* Changed background to surface-color */
            border-radius: 12px;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
            min-height: 70px;
            /* Adjusted min-height */
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            /* Added box-shadow */
        }

        .partner-logo:hover {
            opacity: 1;
            transform: translateY(-4px);
            border-color: var(--accent-color);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
            /* Enhanced box-shadow on hover */
        }

        .partner-logo img {
            max-width: 120px;
            max-height: 40px;
            filter: grayscale(100%) brightness(0.8);
            transition: filter 0.3s ease;
        }

        .partner-logo:hover img {
            filter: grayscale(0%) brightness(1);
        }

        /* Placeholder for partner logos */
        .partner-placeholder {
            width: 120px;
            height: 40px;
            background: linear-gradient(135deg, var(--text-secondary), var(--border-color));
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--bg-color);
            font-weight: 600;
            font-size: 12px;
            font-family: '"Work Sans"', sans-serif;
            /* Added font family */
        }

        /* ------------------- */
        /* --- Hero Section --- */
        /* ------------------- */
        .hero {
            text-align: center;
            padding-top: 120px;
            padding-bottom: 180px;
            position: relative;
            overflow: hidden;
            width: 100vw;
            margin-left: calc(-50vw + 50%);
            background:
                linear-gradient(180deg, rgba(15, 15, 20, 0.4) 0%, rgba(8, 8, 12, 0.98) 100%),
                linear-gradient(0deg, rgba(99, 102, 241, 0.22) 0%, rgba(99, 102, 241, 0.08) 50%, transparent 100%),
                linear-gradient(0deg, rgba(168, 85, 247, 0.18) 0%, rgba(168, 85, 247, 0.06) 70%, transparent 100%),
                linear-gradient(0deg, rgba(59, 130, 246, 0.16) 0%, rgba(59, 130, 246, 0.05) 80%, transparent 100%);
            animation: taskadeBackgroundShift 15s ease-in-out infinite;
            /* Speed adjusted */
            background-size: cover, cover, cover;
            background-position: center, center, center;
            background-attachment: fixed;
        }

        /* Animated Background Layers */
        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.03)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            /* Stroke width changed to 0.5 */
            opacity: 0.4;
            pointer-events: none;
            animation: gridPulse 16s ease-in-out infinite;
            /* Speed adjusted */
        }

        .hero::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                linear-gradient(0deg, rgba(99, 102, 241, 0.16) 0%, transparent 60%),
                linear-gradient(0deg, rgba(168, 85, 247, 0.12) 0%, transparent 80%),
                linear-gradient(0deg, rgba(59, 130, 246, 0.14) 0%, transparent 70%),
                linear-gradient(0deg, rgba(147, 197, 253, 0.09) 0%, transparent 90%);
            pointer-events: none;
            animation: taskadeOverlayShift 36s ease-in-out infinite;
            /* Speed adjusted */
        }

        /* Floating Particles */
        .hero-particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(74, 85, 255, 0.6);
            border-radius: 50%;
            animation: floatUp 20s linear infinite;
            /* Speed adjusted */
        }

        .particle:nth-child(2n) {
            background: rgba(255, 107, 107, 0.4);
            animation-duration: 40s;
            /* Speed adjusted */
            animation-delay: -5s;
        }

        .particle:nth-child(3n) {
            background: rgba(138, 146, 255, 0.5);
            animation-duration: 18s;
            /* Speed adjusted */
            animation-delay: -10s;
        }



        /* Glowing Orbs */
        .hero-orbs {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }

        .glowing-orb {
            position: absolute;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(74, 85, 255, 0.8);
            box-shadow: 0 0 20px rgba(74, 85, 255, 0.5);
            animation: orbFloat 12s ease-in-out infinite;
            /* Speed adjusted */
        }

        .glowing-orb:nth-child(2n) {
            background: rgba(255, 107, 107, 0.6);
            box-shadow: 0 0 20px rgba(255, 107, 107, 0.4);
            animation-duration: 30s;
            /* Speed adjusted */
            animation-delay: -5s;
        }

        .glowing-orb:nth-child(3n) {
            background: rgba(138, 146, 255, 0.7);
            box-shadow: 0 0 20px rgba(138, 146, 255, 0.5);
            animation-duration: 36s;
            /* Speed adjusted */
            animation-delay: -8s;
        }

        /* Data Stream Effect */
        .hero-datastream {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }

        .data-line {
            position: absolute;
            width: 1px;
            height: 100px;
            background: linear-gradient(to bottom, transparent, rgba(74, 85, 255, 0.8), transparent);
            animation: dataFlow 10s linear infinite;
            /* Speed adjusted */
        }

        .data-line:nth-child(2n) {
            background: linear-gradient(to bottom, transparent, rgba(255, 107, 107, 0.6), transparent);
            animation-duration: 12s;
            /* Speed adjusted */
            animation-delay: -3s;
        }

        .data-line:nth-child(3n) {
            background: linear-gradient(to bottom, transparent, rgba(138, 146, 255, 0.7), transparent);
            animation-duration: 10s;
            /* Speed adjusted */
            animation-delay: -6s;
        }

        @keyframes dataFlow {
            0% {
                transform: translateY(-100px);
                opacity: 0;
            }

            10%,
            90% {
                opacity: 1;
            }

            100% {
                transform: translateY(calc(100vh + 100px));
                opacity: 0;
            }
        }

        .hero-content {
            position: relative;
            z-index: 1;
            max-width: var(--container-width);
            margin: 0 auto;
            padding: 0 5%;
        }

        .hero h1 {
            font-size: 72px;
            max-width: 900px;
            font-weight: bold !important;
            margin: 0 auto 32px auto;
            line-height: 1.1;
            font-weight: 200;
            letter-spacing: -0.02em;
            color: #FFFFFF;
        }

        .hero h1 .gradient-text {
            background: linear-gradient(45deg,
                    #714DFF 0%,
                    #9C83FF 15%,
                    #E151FF 30%,
                    #ff9719 45%,
                    #ff11e3 60%,
                    #714DFF 75%,
                    #9C83FF 90%,
                    #E151FF 100%);
            background-size: 300% 300%;
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: gradientShift 8s ease-in-out infinite;
            /* Speed adjusted */
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .hero h1 .gradient-text:hover {
            animation-duration: 2s;
            /* Speed adjusted */
            transform: scale(1.05);
        }

        @keyframes gradientShift {

            0%,
            100% {
                background-position: 0% 50%;
            }

            25% {
                background-position: 100% 50%;
            }

            50% {
                background-position: 50% 100%;
            }

            75% {
                background-position: 50% 0%;
            }
        }

        /* Hero Animation Keyframes */
        @keyframes gridPulse {

            0%,
            100% {
                opacity: 0.4;
                transform: scale(1);
            }

            50% {
                opacity: 0.6;
                transform: scale(1.02);
            }
        }

        @keyframes taskadeOverlayShift {

            0%,
            100% {
                background:
                    linear-gradient(0deg, rgba(99, 102, 241, 0.16) 0%, transparent 60%),
                    linear-gradient(0deg, rgba(168, 85, 247, 0.12) 0%, transparent 80%),
                    linear-gradient(0deg, rgba(59, 130, 246, 0.14) 0%, transparent 70%),
                    linear-gradient(0deg, rgba(147, 197, 253, 0.09) 0%, transparent 90%);
                opacity: 0.85;
            }

            25% {
                background:
                    linear-gradient(0deg, rgba(139, 92, 246, 0.19) 0%, transparent 65%),
                    linear-gradient(0deg, rgba(99, 102, 241, 0.15) 0%, transparent 75%),
                    linear-gradient(0deg, rgba(147, 197, 253, 0.11) 0%, transparent 85%),
                    linear-gradient(0deg, rgba(59, 130, 246, 0.10) 0%, transparent 92%);
                opacity: 0.9;
            }

            50% {
                background:
                    linear-gradient(0deg, rgba(59, 130, 246, 0.18) 0%, transparent 62%),
                    linear-gradient(0deg, rgba(139, 92, 246, 0.14) 0%, transparent 78%),
                    linear-gradient(0deg, rgba(168, 85, 247, 0.11) 0%, transparent 88%),
                    linear-gradient(0deg, rgba(99, 102, 241, 0.09) 0%, transparent 94%);
                opacity: 0.87;
            }

            75% {
                background:
                    linear-gradient(0deg, rgba(147, 197, 253, 0.16) 0%, transparent 68%),
                    linear-gradient(0deg, rgba(59, 130, 246, 0.13) 0%, transparent 82%),
                    linear-gradient(0deg, rgba(99, 102, 241, 0.11) 0%, transparent 86%),
                    linear-gradient(0deg, rgba(139, 92, 246, 0.09) 0%, transparent 95%);
                opacity: 0.9;
            }
        }

        @keyframes floatUp {

            /* Speed adjusted */
            0% {
                transform: translateY(100vh) translateX(0px) rotate(0deg);
                opacity: 0;
            }

            10% {
                opacity: 1;
            }

            90% {
                opacity: 1;
            }

            100% {
                transform: translateY(-100px) translateX(100px) rotate(360deg);
                opacity: 0;
            }
        }



        @keyframes orbFloat {

            /* Speed adjusted */
            0%,
            100% {
                transform: translateY(0px) translateX(0px) scale(1);
                opacity: 0.8;
            }

            25% {
                transform: translateY(-20px) translateX(10px) scale(1.2);
                opacity: 1;
            }

            50% {
                transform: translateY(-10px) translateX(-10px) scale(0.8);
                opacity: 0.6;
            }

            75% {
                transform: translateY(-30px) translateX(5px) scale(1.1);
                opacity: 0.9;
            }
        }

        @keyframes taskadeBackgroundShift {

            /* Speed adjusted */
            0%,
            100% {
                background:
                    linear-gradient(180deg, rgba(15, 15, 20, 0.4) 0%, rgba(8, 8, 12, 0.98) 100%),
                    linear-gradient(0deg, rgba(99, 102, 241, 0.22) 0%, rgba(99, 102, 241, 0.08) 50%, transparent 100%),
                    linear-gradient(0deg, rgba(168, 85, 247, 0.18) 0%, rgba(168, 85, 247, 0.06) 70%, transparent 100%),
                    linear-gradient(0deg, rgba(59, 130, 246, 0.16) 0%, rgba(59, 130, 246, 0.05) 80%, transparent 100%);
            }

            20% {
                background:
                    linear-gradient(180deg, rgba(15, 15, 20, 0.4) 0%, rgba(8, 8, 12, 0.98) 100%),
                    linear-gradient(0deg, rgba(139, 92, 246, 0.25) 0%, rgba(139, 92, 246, 0.09) 55%, transparent 100%),
                    linear-gradient(0deg, rgba(99, 102, 241, 0.20) 0%, rgba(99, 102, 241, 0.07) 65%, transparent 100%),
                    linear-gradient(0deg, rgba(147, 197, 253, 0.12) 0%, rgba(147, 197, 253, 0.04) 85%, transparent 100%);
            }

            40% {
                background:
                    linear-gradient(180deg, rgba(15, 15, 20, 0.4) 0%, rgba(8, 8, 12, 0.98) 100%),
                    linear-gradient(0deg, rgba(59, 130, 246, 0.24) 0%, rgba(59, 130, 246, 0.08) 52%, transparent 100%),
                    linear-gradient(0deg, rgba(139, 92, 246, 0.19) 0%, rgba(139, 92, 246, 0.06) 72%, transparent 100%),
                    linear-gradient(0deg, rgba(168, 85, 247, 0.16) 0%, rgba(168, 85, 247, 0.05) 78%, transparent 100%);
            }

            60% {
                background:
                    linear-gradient(180deg, rgba(15, 15, 20, 0.4) 0%, rgba(8, 8, 12, 0.98) 100%),
                    linear-gradient(0deg, rgba(147, 197, 253, 0.22) 0%, rgba(147, 197, 253, 0.08) 58%, transparent 100%),
                    linear-gradient(0deg, rgba(59, 130, 246, 0.18) 0%, rgba(59, 130, 246, 0.06) 68%, transparent 100%),
                    linear-gradient(0deg, rgba(99, 102, 241, 0.15) 0%, rgba(99, 102, 241, 0.05) 82%, transparent 100%);
            }

            80% {
                background:
                    linear-gradient(180deg, rgba(15, 15, 20, 0.4) 0%, rgba(8, 8, 12, 0.98) 100%),
                    linear-gradient(0deg, rgba(168, 85, 247, 0.24) 0%, rgba(168, 85, 247, 0.09) 54%, transparent 100%),
                    linear-gradient(0deg, rgba(147, 197, 253, 0.19) 0%, rgba(147, 197, 253, 0.06) 74%, transparent 100%),
                    linear-gradient(0deg, rgba(139, 92, 246, 0.13) 0%, rgba(139, 92, 246, 0.04) 84%, transparent 100%);
            }
        }

        @keyframes pulse {

            0%,
            100% {
                transform: scale(1);
                opacity: 0.7;
            }

            50% {
                transform: scale(1.1);
                opacity: 1;
            }
        }

        .hero .subtitle {
            font-size: 22px;
            max-width: 700px;
            margin: 0 auto 48px auto;
            color: var(--text-secondary);
            line-height: 1.5;
        }

        .hero .cta-group {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 80px;
        }

        /* ------------------- */
        /* --- Problem Section --- */
        /* ------------------- */
        .problem-section {
            background-color: var(--bg-color);
            padding: 50px 0;
        }

        .problem-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 32px;
        }

        .problem-card {
            background: linear-gradient(135deg, var(--surface-color) 0%, rgba(26, 26, 34, 0.5) 100%);
            padding: 40px;
            border-radius: 16px;
            border: 1px solid var(--border-color);
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }

        .problem-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, #8B5CF6, #A78BFA);
            transform: scaleX(0);
            transition: transform 0.4s ease;
        }

        .problem-card:hover::before {
            transform: scaleX(1);
        }

        .problem-card:hover {
            transform: translateY(-6px);
            box-shadow: 0 16px 40px rgba(139, 92, 246, 0.15);
            border-color: rgba(139, 92, 246, 0.4);
        }

        .problem-card .icon {
            margin-bottom: 24px;
            width: 96px;
            height: 96px;
            background: transparent;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .problem-card .icon svg {
            width: 80px;
            height: 80px;
            fill: #8b5cf6;
        }

        .problem-card h3 {
            font-size: 22px;
            margin-bottom: 16px;
            color: var(--text-primary);
        }

        .problem-card p {
            color: var(--text-secondary);
            line-height: 1.6;
        }

        /* ------------------- */
        /* --- Partners Section --- */
        /* ------------------- */
        .partners-section {
            padding: 40px 0;
            /* Adjusted padding */
            background-color: var(--surface-color);
            border-top: 1px solid var(--border-color);
            border-bottom: 1px solid var(--border-color);
        }

        .process-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 40px;
            margin-top: 64px;
        }

        .process-step {
            text-align: center;
            padding: 32px;
            border-radius: 12px;
            background: linear-gradient(135deg, var(--surface-color) 0%, rgba(26, 26, 34, 0.8) 100%);
            border: 1px solid var(--border-color);
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }

        .process-step::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--accent-color), var(--accent-hover));
            transform: scaleX(0);
            transition: transform 0.4s ease;
        }

        .process-step:hover::before {
            transform: scaleX(1);
        }

        .process-step:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 60px rgba(74, 85, 255, 0.15);
            border-color: rgba(74, 85, 255, 0.3);
        }

        .process-step .number {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-family: 'Plus Jakarta Sans', 'Work Sans', sans-serif;
            font-size: 50px;
            font-weight: 700;
            color: transparent;
            margin-bottom: 20px;
            margin-top: -8px;
            position: relative;
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background-color: #000000;
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .process-step .number::before {
            content: attr(data-number);
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-family: 'Plus Jakarta Sans', 'Work Sans', sans-serif;
            font-size: 50px;
            font-weight: 700;
            background: linear-gradient(135deg, #b366cf 0%, #8B5CF6 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            z-index: 2;
        }

        .process-step .number::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border-radius: 50%;
            background: linear-gradient(135deg, #b366cf 0%, #8B5CF6 100%);
            z-index: -1;
            opacity: 0.1;
        }



        .process-step h3 {
            font-size: 22px;
            margin-bottom: 12px;
        }

        .process-step p {
            color: var(--text-secondary);
        }


        /* ------------------- */
        /* --- Our Solutions Section --- */
        /* ------------------- */
        .solutions-section {
            background-color: var(--bg-color);
        }

        .solutions-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 80px 40px;
            margin: 0;
        }

        .solution-card {
            background: transparent;
            border-radius: 0;
            overflow: visible;
            transition: all 0.3s ease;
            position: relative;
            border: none;
            box-shadow: none;
        }

        .solution-card:hover {
            transform: translateY(-5px);
        }

        .solution-image {
            width: 100%;
            height: 220px;
            object-fit: cover;
            border-radius: 16px;
            margin-bottom: 20px;
            transition: transform 0.3s ease;
        }

        .solution-card:hover .solution-image {
            transform: scale(1.02);
        }

        .solution-content {
            padding: 0;
        }

        .solution-card h3 {
            font-size: 25px;
            font-weight: 600;
            margin-bottom: 12px;
            line-height: 1.3;
            min-height: 52px;
            display: flex;
            align-items: center;
        }

        .solution-card .description {
            font-size: 16px;
            line-height: 1.6;
            color: var(--text-secondary);
            margin-bottom: 16px;
            min-height: 84px;
        }

        .solution-card .time-saved {
            font-size: 14px;
            font-weight: 600;
            color: #FFFFFF;
            background: rgba(74, 85, 255, 0.1);
            padding: 6px 12px;
            border-radius: 10px;
            display: inline-block;
        }



        .solution-card .description {
            color: var(--text-secondary);
            margin-bottom: 16px;
            line-height: 1.6;
        }

        .solution-card .time-saved {
            background: linear-gradient(135deg, rgba(74, 85, 255, 0.1), rgba(74, 85, 255, 0.2));
            border: 1px solid var(--accent-color);
            border-radius: 10px;
            padding:5px 16px;
            font-size: 14px;
            font-weight: 600;
            color: #FFFFFF;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            backdrop-filter: blur(10px);
        }

        .solution-card .time-saved::before {
            content: '⚡';
            font-size: 22px;
        }

        /* ------------------- */
        /* --- Case Studies Section --- */
        /* ------------------- */
        #case-studies {
            position: relative;
            background:
                linear-gradient(135deg, rgba(11, 11, 15, 0.90) 0%, rgba(26, 26, 34, 0.95) 100%),
                radial-gradient(ellipse at 20% 80%, rgba(80, 26, 200, 0.15) 0%, transparent 60%),
                radial-gradient(ellipse at 80% 20%, rgba(179, 102, 207, 0.12) 0%, transparent 70%),
                radial-gradient(ellipse at 50% 50%, rgba(67, 83, 255, 0.10) 0%, transparent 80%);
            border-top: 1px solid var(--border-color);
            overflow: hidden;
            animation: subtleBackgroundShift 30s ease-in-out infinite;
        }

        #case-studies::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                linear-gradient(45deg, transparent 30%, rgba(80, 26, 200, 0.08) 50%, transparent 70%),
                linear-gradient(-45deg, transparent 40%, rgba(179, 102, 207, 0.06) 60%, transparent 80%),
                linear-gradient(90deg, transparent 35%, rgba(67, 83, 255, 0.06) 55%, transparent 75%);
            z-index: 1;
            animation: subtleColorShift 25s ease-in-out infinite;
        }

        #case-studies::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 30% 40%, rgba(80, 26, 200, 0.06) 0%, transparent 50%),
                radial-gradient(circle at 70% 60%, rgba(179, 102, 207, 0.05) 0%, transparent 55%),
                radial-gradient(circle at 50% 80%, rgba(67, 83, 255, 0.04) 0%, transparent 60%);
            z-index: 2;
            animation: subtleParticleFloat 20s ease-in-out infinite;
        }

        #case-studies .container {
            position: relative;
            z-index: 3;
        }

        /* Mobile optimization for video */
        @media (max-width: 768px) {
            .video-background video {
                opacity: 0.2;
            }
        }

        @keyframes subtleBackgroundShift {
            0%, 100% {
                background:
                    linear-gradient(135deg, rgba(11, 11, 15, 0.90) 0%, rgba(26, 26, 34, 0.95) 100%),
                    radial-gradient(ellipse at 20% 80%, rgba(80, 26, 200, 0.15) 0%, transparent 60%),
                    radial-gradient(ellipse at 80% 20%, rgba(179, 102, 207, 0.12) 0%, transparent 70%),
                    radial-gradient(ellipse at 50% 50%, rgba(67, 83, 255, 0.10) 0%, transparent 80%);
            }
            33% {
                background:
                    linear-gradient(135deg, rgba(11, 11, 15, 0.90) 0%, rgba(26, 26, 34, 0.95) 100%),
                    radial-gradient(ellipse at 70% 30%, rgba(179, 102, 207, 0.18) 0%, transparent 65%),
                    radial-gradient(ellipse at 30% 70%, rgba(67, 83, 255, 0.14) 0%, transparent 75%),
                    radial-gradient(ellipse at 90% 90%, rgba(80, 26, 200, 0.12) 0%, transparent 70%);
            }
            66% {
                background:
                    linear-gradient(135deg, rgba(11, 11, 15, 0.90) 0%, rgba(26, 26, 34, 0.95) 100%),
                    radial-gradient(ellipse at 60% 40%, rgba(67, 83, 255, 0.16) 0%, transparent 62%),
                    radial-gradient(ellipse at 40% 60%, rgba(80, 26, 200, 0.14) 0%, transparent 68%),
                    radial-gradient(ellipse at 10% 10%, rgba(179, 102, 207, 0.10) 0%, transparent 85%);
            }
        }

        @keyframes subtleColorShift {
            0%, 100% {
                background:
                    linear-gradient(45deg, transparent 30%, rgba(80, 26, 200, 0.08) 50%, transparent 70%),
                    linear-gradient(-45deg, transparent 40%, rgba(179, 102, 207, 0.06) 60%, transparent 80%),
                    linear-gradient(90deg, transparent 35%, rgba(67, 83, 255, 0.06) 55%, transparent 75%);
            }
            50% {
                background:
                    linear-gradient(45deg, transparent 25%, rgba(179, 102, 207, 0.10) 55%, transparent 75%),
                    linear-gradient(-45deg, transparent 35%, rgba(67, 83, 255, 0.08) 65%, transparent 85%),
                    linear-gradient(90deg, transparent 30%, rgba(80, 26, 200, 0.08) 60%, transparent 80%);
            }
        }

        @keyframes subtleParticleFloat {
            0%, 100% {
                background:
                    radial-gradient(circle at 30% 40%, rgba(80, 26, 200, 0.06) 0%, transparent 50%),
                    radial-gradient(circle at 70% 60%, rgba(179, 102, 207, 0.05) 0%, transparent 55%),
                    radial-gradient(circle at 50% 80%, rgba(67, 83, 255, 0.04) 0%, transparent 60%);
            }
            33% {
                background:
                    radial-gradient(circle at 60% 20%, rgba(179, 102, 207, 0.07) 0%, transparent 45%),
                    radial-gradient(circle at 20% 70%, rgba(67, 83, 255, 0.06) 0%, transparent 50%),
                    radial-gradient(circle at 80% 50%, rgba(80, 26, 200, 0.05) 0%, transparent 65%);
            }
            66% {
                background:
                    radial-gradient(circle at 40% 80%, rgba(67, 83, 255, 0.06) 0%, transparent 48%),
                    radial-gradient(circle at 80% 30%, rgba(80, 26, 200, 0.056) 0%, transparent 52%),
                    radial-gradient(circle at 10% 60%, rgba(179, 102, 207, 0.044) 0%, transparent 58%);
            }
        }

        #case-studies .container {
            position: relative;
            z-index: 3;
        }

        .case-study-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
        }

        .case-study-card {
            background-color: var(--bg-color);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .case-study-card .category {
            font-size: 14px;
            font-weight: 500;
            color: var(--accent-color);
            margin-bottom: 16px;
        }

        .case-study-card .metric {
            font-family: 'Plus Jakarta Sans', 'Work Sans', sans-serif;
            font-size: 48px;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 8px;
        }

        .case-study-card .metric-label {
            font-size: 16px;
            color: var(--text-secondary);
            margin-bottom: 32px;
        }

        .case-study-card .testimonial {
            font-style: italic;
            color: #C0C0C0;
            border-left: 3px solid var(--accent-color);
            padding-left: 20px;
            margin-top: auto;
        }

        .case-study-card .testimonial-author {
            font-style: normal;
            font-weight: 600;
            color: var(--text-primary);
            margin-top: 16px;
            display: block;
        }

        /* ------------------- */
        /* --- CTA Section --- */
        /* ------------------- */
        .cta-section {
            text-align: center;
        }

        .cta-section .section-title {
            font-size: 54px;
            max-width: 950px;
            margin-left: auto;
            margin-right: auto;
        }

        .cta-section .section-subtitle {
            margin-bottom: 40px;
        }



        /* ------------------- */
        /* --- Footer --- */
        /* ------------------- */
        .footer {
            border-top: 1px solid var(--border-color);
            padding: 40px 0;
            color: var(--text-secondary);
            font-size: 14px;
        }

        .footer .container {
            padding: 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .footer-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }

        .footer-text {
            flex: 1;
        }

        .footer-logo {
            flex-shrink: 0;
            margin-left: 20px;
        }

        .footer-logo a {
            display: inline-block;
            transition: transform 0.3s ease;
        }

        .footer-logo a:hover {
            transform: scale(1.05);
        }

        .footer-logo img {
            height: 40px;
            width: auto;
            opacity: 0.8;
            transition: opacity 0.3s ease;
        }

        .footer-logo a:hover img {
            opacity: 1;
        }

        /* ------------------- */
        /* --- Responsive Design --- */
        /* ------------------- */
        @media (max-width: 900px) {
            .hero h1 {
                font-size: 48px;
            }

            .hero .subtitle {
                font-size: 18px;
            }

            .section-title {
                font-size: 42px;
            }

            .process-grid {
                grid-template-columns: repeat(3, 1fr);
                gap: 24px;
            }

            .process-step {
                padding: 24px;
            }

            .process-step .number {
                font-size: 44px;
                margin-bottom: 16px;
                margin-top: -6px;
                width: 100px;
                height: 100px;
            }

            .case-study-grid {
                grid-template-columns: 1fr;
            }

            .solutions-grid {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            }

            .partners-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 24px;
            }

            /* Show hamburger menu on tablet and mobile */
            .nav-desktop {
                display: none;
            }

            .hamburger {
                display: flex;
            }

            .nav-mobile {
                display: block;
            }
        }

        @media (max-width: 600px) {
            body {
                padding-top: 70px;
                /* Reduced header height on mobile */
            }

            .header {
                padding: 12px 5%;
            }

            .logo {
                font-size: 20px;
            }

            .hamburger span {
                width: 22px;
            }

            .nav-mobile {
                width: 100%;
                padding: 90px 24px 24px 24px;
            }

            .container {
                padding: 80px 0;
            }

            .hero {
                padding-top: 40px;
                padding-bottom: 80px;
                background-attachment: scroll;
                /* Better performance on mobile */
                background-size: cover, cover, 120%;
                /* Slightly larger for better mobile view */
                margin-left: calc(-50vw + 50%);
                width: 100vw;
            }

            .hero::before {
                opacity: 0.8;
                /* Stronger overlay on mobile for better text readability */
            }

            .hero-content {
                padding: 0 10%;
                /* More padding on mobile for better readability */
            }

            /* Reduce animations on mobile for performance */
            .particle {
                display: none;
            }



            .glowing-orb {
                animation-duration: 20s;
            }

            .hero::before {
                animation-duration: 12s;
            }

            .hero::after {
                animation-duration: 16s;
            }

            .hero h1 {
                font-size: 36px;
            }

            .hero .subtitle {
                font-size: 16px;
            }

            .hero .cta-group {
                flex-direction: column;
            }

            .section-title {
                font-size: 36px;
            }

            .section-subtitle {
                font-size: 16px;
                margin-bottom: 48px;
            }

            .problem-grid {
                grid-template-columns: 1fr;
            }

            .process-grid {
                grid-template-columns: 1fr;
                gap: 32px;
            }

            .process-step {
                padding: 32px 24px;
            }

            .process-step .number {
                font-size: 42px;
                margin-bottom: 14px;
                margin-top: -4px;
                width: 80px;
                height: 80px;
            }

            /* Problem cards icons responsive */
            .problem-card .icon {
                width: 80px;
                height: 80px;
            }

            .problem-card .icon svg {
                width: 60px;
                height: 60px;
            }

            /* Title colors responsive */
            .title-white, .title-pink {
                display: inline;
            }

            .cta-section .section-title {
                font-size: 42px;
            }

            .solutions-grid {
                grid-template-columns: 1fr;
                gap: 60px;
            }

            .solution-image {
                height: 180px;
                border-radius: 12px;
            }

            .solution-card h3 {
                font-size: 22px;
                min-height: auto;
            }

            .solution-card .description {
                min-height: auto;
            }

            .partners-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 16px;
            }

            .partner-placeholder {
                font-size: 11px;
            }

            /* Footer responsive */
            .footer .container {
                flex-direction: column;
                text-align: center;
            }

            .footer-content {
                flex-direction: column;
                gap: 20px;
            }

            .footer-logo {
                margin-left: 0;
            }

            .footer-logo img {
                height: 35px;
            }

            .hero h1 {
                font-size: 48px;
            }

            .hero .subtitle {
                font-size: 18px;
            }

            /* Optimize background for mobile */
            .hero {
                background-attachment: scroll;
                /* Better performance on mobile */
                margin-left: calc(-50vw + 50%);
                width: 100vw;
            }

            .hero-content {
                padding: 0 8%;
                /* More padding on tablet for better readability */
            }
        }

        /* Extra small devices (phones, 480px and down) */
        @media (max-width: 480px) {
            .section-title {
                font-size: 28px;
                line-height: 1.2;
            }

            .hero h1 {
                font-size: 32px;
                line-height: 1.2;
            }

            .process-step .number {
                width: 70px;
                height: 70px;
                font-size: 36px;
            }

            .problem-card .icon {
                width: 64px;
                height: 64px;
            }

            .problem-card .icon svg {
                width: 48px;
                height: 48px;
            }

            .problem-card {
                padding: 20px;
            }

            .solution-card .description {
                font-size: 15px;
            }

            /* Ensure title colors wrap properly */
            .title-white, .title-pink {
                display: inline;
                word-break: break-word;
            }
        }
    </style>
</head>

<body>

    <header class="header">
        <div class="header-container">
            <a href="#hero" class="logo">Agentik.ai</a>

            <!-- Desktop Navigation -->
            <nav class="nav-desktop">
                <a href="#hero">Home</a>
                <a href="#problemi">Problemi</a>
                <a href="#processo">Processo</a>
                <a href="#soluzioni">Soluzioni</a>
                <a href="#case-studies">Risultati</a>
                <a href="contact.html" class="secondary-button">Contatti</a>
            </nav>

            <!-- Mobile Hamburger -->
            <div class="hamburger" onclick="toggleMobileMenu()">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </header>

    <!-- Mobile Navigation Overlay -->
    <div class="nav-overlay" onclick="closeMobileMenu()"></div>

    <!-- Mobile Navigation Menu -->
    <nav class="nav-mobile">
        <a href="#hero" onclick="closeMobileMenu()">Home</a>
        <a href="#problemi" onclick="closeMobileMenu()">Problemi</a>
        <a href="#processo" onclick="closeMobileMenu()">Processo</a>
        <a href="#soluzioni" onclick="closeMobileMenu()">Soluzioni</a>
        <a href="#case-studies" onclick="closeMobileMenu()">Risultati</a>
        <a href="contact.html" onclick="closeMobileMenu()">Contatti</a>
    </nav>

    <main>
        <section id="hero" class="hero">
            <!-- Animated Particles -->
            <div class="hero-particles">
                <div class="particle" style="left: 10%; animation-delay: 0s;"></div>
                <div class="particle" style="left: 20%; animation-delay: -2s;"></div>
                <div class="particle" style="left: 30%; animation-delay: -4s;"></div>
                <div class="particle" style="left: 40%; animation-delay: -6s;"></div>
                <div class="particle" style="left: 50%; animation-delay: -8s;"></div>
                <div class="particle" style="left: 60%; animation-delay: -10s;"></div>
                <div class="particle" style="left: 70%; animation-delay: -12s;"></div>
                <div class="particle" style="left: 80%; animation-delay: -14s;"></div>
                <div class="particle" style="left: 90%; animation-delay: -16s;"></div>
                <div class="particle" style="left: 15%; animation-delay: -3s;"></div>
                <div class="particle" style="left: 25%; animation-delay: -7s;"></div>
                <div class="particle" style="left: 35%; animation-delay: -11s;"></div>
                <div class="particle" style="left: 45%; animation-delay: -15s;"></div>
                <div class="particle" style="left: 55%; animation-delay: -1s;"></div>
                <div class="particle" style="left: 65%; animation-delay: -5s;"></div>
                <div class="particle" style="left: 75%; animation-delay: -9s;"></div>
                <div class="particle" style="left: 85%; animation-delay: -13s;"></div>
            </div>

            <!-- Data Stream Effect -->
            <div class="hero-datastream">
                <div class="data-line" style="left: 5%; animation-delay: 0s;"></div>
                <div class="data-line" style="left: 15%; animation-delay: -2s;"></div>
                <div class="data-line" style="left: 25%; animation-delay: -4s;"></div>
                <div class="data-line" style="left: 35%; animation-delay: -1s;"></div>
                <div class="data-line" style="left: 45%; animation-delay: -6s;"></div>
                <div class="data-line" style="left: 55%; animation-delay: -3s;"></div>
                <div class="data-line" style="left: 65%; animation-delay: -5s;"></div>
                <div class="data-line" style="left: 75%; animation-delay: -7s;"></div>
                <div class="data-line" style="left: 85%; animation-delay: -2.5s;"></div>
                <div class="data-line" style="left: 95%; animation-delay: -4.5s;"></div>
            </div>



            <!-- Glowing Orbs -->
            <div class="hero-orbs">
                <div class="glowing-orb" style="top: 15%; left: 10%; animation-delay: 0s;"></div>
                <div class="glowing-orb" style="top: 25%; left: 80%; animation-delay: -3s;"></div>
                <div class="glowing-orb" style="top: 45%; left: 20%; animation-delay: -6s;"></div>
                <div class="glowing-orb" style="top: 65%; left: 70%; animation-delay: -2s;"></div>
                <div class="glowing-orb" style="top: 75%; left: 30%; animation-delay: -8s;"></div>
                <div class="glowing-orb" style="top: 35%; left: 90%; animation-delay: -4s;"></div>
                <div class="glowing-orb" style="top: 55%; left: 5%; animation-delay: -7s;"></div>
                <div class="glowing-orb" style="top: 85%; left: 60%; animation-delay: -1s;"></div>
            </div>

            <div class="hero-content">
                <h1>Vai Oltre l'Automazione. <br>Raggiungi l'<span class="gradient-text">Autonomia</span>.</h1>
                <p class="subtitle">Progettiamo e implementiamo agenti IA personalizzati per gestire le tue sfide
                    aziendali più complesse—così il tuo team può concentrarsi su ciò che conta davvero.</p>
                <div class="cta-group">
                    <a href="#cta" class="cta-button">
                        Prenota una Chiamata Esplorativa
                        <span>→</span>
                    </a>
                    <a href="#soluzioni" class="secondary-button">
                        <p>Scopri le Soluzioni <span>↓</span></p>
                    </a>
                </div>
            </div>
        </section>

        <section class="partners-section">
            <div class="container-small">
                <p class="partners-title">Tecnologie e Partner di Fiducia</p>
                <div class="partners-grid">
                    <div class="partner-logo">
                        <img src="images/n8n.svg" alt="N8N logo">
                    </div>

                    <div class="partner-logo">
                        <img src="images/aws.svg" alt="AWS logo">
                    </div>
                    <div class="partner-logo">
                        <img src="images/unity.svg" alt="Unity3D logo">
                    </div>

                </div>
            </div>
        </section>

        <section id="problemi" class="problem-section">
            <div class="container">
                <h2 class="section-title">
                    <span class="title-white">La tua crescita</span>
                    <span class="title-pink">è limitata dal lavoro manuale?</span>
                </h2>
                <p class="section-subtitle">Le tue persone migliori sono preziose. Tuttavia, spesso sono intrappolate in
                    compiti ripetitivi e di basso impatto che consumano tempo e morale.</p>
                <div class="problem-grid">
                    <div class="problem-card">
                        <div class="icon">
                            <svg xmlns="http://www.w3.org/2000/svg" width="72" height="72" viewBox="0 0 82.228 82.229" fill="currentColor">
                                <defs>
                                    <linearGradient id="flowchart-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#b366cf;stop-opacity:1" />
                                        <stop offset="100%" style="stop-color:#4353ff;stop-opacity:1" />
                                    </linearGradient>
                                </defs>
                                <g transform="translate(-8.589 -8.727)">
                                    <path d="M82.953,27.578a1.175,1.175,0,0,0-.367,1.617c9.023,13.965,7.547,33.555-3.5,46.016l-.113-4.8a1.172,1.172,0,0,0-2.344.055l.18,7.691A1.183,1.183,0,0,0,77.98,79.3c.008,0,7.7-.184,7.719-.18a1.172,1.172,0,0,0-.055-2.344l-4.91.117c11.824-13.207,13.438-34.07,3.836-48.938a1.175,1.175,0,0,0-1.617-.367Z"/>
                                    <path d="M70.352,82.723c-13.965,9.023-33.555,7.551-46.016-3.5l4.8-.113a1.172,1.172,0,0,0-.055-2.344l-7.687.18a1.175,1.175,0,0,0-1.141,1.2l.18,7.688a1.172,1.172,0,0,0,2.344-.055l-.117-4.906C35.879,92.692,56.7,94.317,71.6,84.707a1.173,1.173,0,0,0-1.25-1.984Z"/>
                                    <path d="M27.809,14.973a1.173,1.173,0,0,0,1.25,1.984c13.965-9.019,33.555-7.547,46.016,3.5l-4.8.113a1.172,1.172,0,0,0,.027,2.344c0,.008,7.711-.187,7.719-.18a1.174,1.174,0,0,0,1.145-1.2l-.18-7.691a1.172,1.172,0,0,0-2.344.055l.117,4.91C63.548,6.985,42.685,5.371,27.817,14.973Z"/>
                                    <path d="M11.1,45.711a38.857,38.857,0,0,1,9.219-21.234l.113,4.8a1.172,1.172,0,1,0,2.344-.055l-.18-7.691a1.183,1.183,0,0,0-.359-.812,1.164,1.164,0,0,0-.84-.328l-7.691.18a1.172,1.172,0,0,0,.055,2.344l4.91-.117C6.844,36.008,5.234,56.871,14.836,71.739a1.173,1.173,0,0,0,1.984-1.25A38.966,38.966,0,0,1,11.1,45.716Z"/>
                                    <path d="M60.238,40.594a1.818,1.818,0,0,0,1.875-1.273l1.1-3.512a1.8,1.8,0,0,0-.852-2.117L60.32,32.559a13.135,13.135,0,0,0-.184-1.883l1.789-1.5a1.819,1.819,0,0,0,.43-2.242L60.6,23.7a1.819,1.819,0,0,0-2.086-.887l-2.3.625A13.277,13.277,0,0,0,54.7,22.219l.18-2.277a1.82,1.82,0,0,0-1.3-1.871l-3.6-1.062a1.828,1.828,0,0,0-2.1.832l-1.172,2.012a13.085,13.085,0,0,0-1.988.2l-1.547-1.754a1.833,1.833,0,0,0-2.223-.406l-3.328,1.738a1.8,1.8,0,0,0-.906,2.086l.617,2.2a12.867,12.867,0,0,0-1.25,1.488l-2.375-.168a1.823,1.823,0,0,0-1.875,1.27l-1.1,3.512a1.808,1.808,0,0,0,.848,2.117l2.047,1.129a13.717,13.717,0,0,0,.18,1.883l-1.789,1.5a1.808,1.808,0,0,0-.426,2.242l1.758,3.234a1.826,1.826,0,0,0,2.086.891l2.3-.625A13.277,13.277,0,0,0,39.25,43.61l-.18,2.277a1.818,1.818,0,0,0,1.3,1.871l3.6,1.063a1.831,1.831,0,0,0,2.1-.832l1.172-2.012a13.234,13.234,0,0,0,1.992-.2l1.547,1.758a1.84,1.84,0,0,0,2.219.4l3.32-1.734a1.8,1.8,0,0,0,.91-2.086l-.617-2.2a13.475,13.475,0,0,0,1.25-1.488l2.375.172Zm-4.129-1.742a10.775,10.775,0,0,1-1.359,1.609,1.776,1.776,0,0,0-.477,1.773l.586,2.086-2.586,1.352-1.461-1.66a1.829,1.829,0,0,0-1.727-.594,11.52,11.52,0,0,1-2.125.207,1.821,1.821,0,0,0-1.578.906l-1.117,1.918-2.8-.828.172-2.148a1.792,1.792,0,0,0-.816-1.656A10.987,10.987,0,0,1,39.179,40.5a1.846,1.846,0,0,0-1.762-.457l-2.184.594-1.352-2.484,1.7-1.422A1.81,1.81,0,0,0,36.187,35a10.288,10.288,0,0,1-.2-2.062,1.808,1.808,0,0,0-.937-1.582L33.121,30.3l.844-2.7,1.965.141a1.939,1.939,0,0,0,1.934-.766A10.794,10.794,0,0,1,39.23,25.36a1.808,1.808,0,0,0,.469-1.766l-.586-2.086L41.7,20.157l1.469,1.664a1.827,1.827,0,0,0,1.727.586,11.487,11.487,0,0,1,2.125-.207,1.827,1.827,0,0,0,1.578-.906l1.117-1.914,2.8.828-.172,2.148a1.8,1.8,0,0,0,.816,1.656A10.744,10.744,0,0,1,54.8,25.324a1.842,1.842,0,0,0,1.758.457l2.188-.594L60.1,27.664l-1.7,1.43a1.822,1.822,0,0,0-.6,1.727,10.653,10.653,0,0,1,.2,2.055,1.791,1.791,0,0,0,.934,1.59l1.934,1.07-.844,2.7-2.25-.16a1.832,1.832,0,0,0-1.641.785Z"/>
                                    <path d="M54.285,29.18c-5.238-9.5-19.242-2.352-14.621,7.461A8.207,8.207,0,0,0,54.285,29.18ZM49.64,38.137c-7.012,3.3-12.121-6.7-5.328-10.445a5.784,5.784,0,0,1,2.656-.641,5.873,5.873,0,0,1,2.672,11.086Z"/>
                                    <path d="M24.281,60.648a1.735,1.735,0,0,0-1.2,1.789l.262,3.172a1.722,1.722,0,0,0,1.508,1.563l1.91.227a10.961,10.961,0,0,0,.688,1.3l-.9,1.707a1.733,1.733,0,0,0,.43,2.129l2.461,2.012a1.732,1.732,0,0,0,2.156.035l1.562-1.2a11.214,11.214,0,0,0,1.461.438l.57,1.793A1.739,1.739,0,0,0,37,76.808l3.227-.32A1.75,1.75,0,0,0,41.777,75l.27-1.9a12.126,12.126,0,0,0,1.391-.73l1.73.867a1.751,1.751,0,0,0,2.1-.441l2.09-2.465a1.729,1.729,0,0,0,.055-2.172l-1.156-1.488a11.553,11.553,0,0,0,.469-1.445l1.875-.609a1.729,1.729,0,0,0,1.2-1.789l-.258-3.172a1.733,1.733,0,0,0-1.508-1.562l-1.91-.227a10.877,10.877,0,0,0-.684-1.3l.9-1.7a1.722,1.722,0,0,0-.426-2.133l-2.461-2.016a1.722,1.722,0,0,0-2.156-.031l-1.566,1.2a11.213,11.213,0,0,0-1.461-.437l-.57-1.8a1.723,1.723,0,0,0-1.812-1.184l-3.219.32A1.735,1.735,0,0,0,33.1,50.261l-.27,1.9a11.23,11.23,0,0,0-1.391.73l-1.727-.859a1.735,1.735,0,0,0-2.1.438l-2.1,2.477a1.733,1.733,0,0,0-.047,2.16L26.621,58.6a12.452,12.452,0,0,0-.469,1.445l-1.871.609ZM27.2,62.164c1.328-.4,1.172-1.949,1.723-2.992a1.727,1.727,0,0,0-.219-1.727l-1.07-1.379L29.1,54.339l1.6.8a1.737,1.737,0,0,0,1.731-.1,9.508,9.508,0,0,1,1.617-.848,1.759,1.759,0,0,0,1.059-1.359l.25-1.773,2.258-.227.527,1.66a1.719,1.719,0,0,0,1.313,1.164,9.021,9.021,0,0,1,1.707.508,1.74,1.74,0,0,0,1.715-.227l1.457-1.109,1.688,1.383L45.2,55.781a1.708,1.708,0,0,0,.086,1.746,8.644,8.644,0,0,1,.813,1.555,1.728,1.728,0,0,0,1.391,1.063l1.762.211.18,2.176-1.723.563a1.744,1.744,0,0,0-1.176,1.281,8.949,8.949,0,0,1-.555,1.7,1.715,1.715,0,0,0,.219,1.734l1.07,1.375-1.469,1.73-1.609-.8a1.741,1.741,0,0,0-1.719.1,9.6,9.6,0,0,1-1.609.844,1.753,1.753,0,0,0-1.07,1.367l-.25,1.773-2.258.227-.531-1.664a1.709,1.709,0,0,0-1.312-1.16c-.816-.094-1.547-.574-2.359-.637a1.793,1.793,0,0,0-1.066.355l-1.453,1.109-1.691-1.383.832-1.578a1.718,1.718,0,0,0-.086-1.742,8.535,8.535,0,0,1-.816-1.551,1.7,1.7,0,0,0-1.383-1.062L25.636,64.9l-.18-2.172,1.734-.566Z"/>
                                    <path d="M32.707,68.109a7.258,7.258,0,0,0,10.223-.746c4.215-4.766.32-12.516-6.012-11.969C30.571,55.769,27.844,64,32.707,68.1Zm1.027-8.687a4.872,4.872,0,0,1,3.719-1.7,4.923,4.923,0,0,1,3.7,8.109c-4.391,4.77-11.5-1.375-7.422-6.414Z"/>
                                    <path d="M79.855,61.164l1.887-2.3a1.688,1.688,0,0,0-.008-2.125l-1.109-1.359a10.659,10.659,0,0,0,.367-1.25l1.672-.547a1.684,1.684,0,0,0,1.148-1.789l-.348-2.953a1.687,1.687,0,0,0-1.492-1.492l-1.785-.211a10.884,10.884,0,0,0-.66-1.176l.762-1.539a1.693,1.693,0,0,0-.469-2.07l-2.375-1.875a1.7,1.7,0,0,0-2.1-.008l-1.391,1.078a11.366,11.366,0,0,0-1.336-.391l-.59-1.664a1.7,1.7,0,0,0-1.773-1.117l-3.016.3a1.7,1.7,0,0,0-1.508,1.492l-.187,1.711a11.531,11.531,0,0,0-1.187.633l-1.617-.789a1.7,1.7,0,0,0-2.062.445l-1.883,2.3a1.677,1.677,0,0,0,0,2.125L59.9,47.953a10.659,10.659,0,0,0-.367,1.25l-1.672.547a1.684,1.684,0,0,0-1.148,1.789l.348,2.953a1.7,1.7,0,0,0,1.5,1.492l1.785.211a10.474,10.474,0,0,0,.664,1.176l-.762,1.535a1.691,1.691,0,0,0,.469,2.07l2.375,1.875a1.7,1.7,0,0,0,2.1.008l1.383-1.078a11.366,11.366,0,0,0,1.336.391l.59,1.664a1.71,1.71,0,0,0,1.777,1.117l3.016-.3A1.673,1.673,0,0,0,74.8,63.16l.188-1.711a11.141,11.141,0,0,0,1.188-.633l1.621.789a1.7,1.7,0,0,0,2.055-.449Zm-4.66-2.488c-.883.672-2.383.813-2.492,2.148l-.172,1.559-1.988.2L70,61.066a1.691,1.691,0,0,0-1.289-1.109c-.781-.09-1.48-.539-2.254-.594-.824-.059-1.687.926-2.316,1.352l-1.57-1.242.7-1.406c.625-1.164-.559-2.117-.945-3.148A1.721,1.721,0,0,0,60.96,53.91l-1.641-.191L59.1,51.813l1.531-.5a1.678,1.678,0,0,0,1.121-1.285,8.458,8.458,0,0,1,.453-1.555,1.667,1.667,0,0,0-.258-1.7l-1.012-1.242,1.215-1.484s1.266.617,1.273.617a1.773,1.773,0,0,0,1.9-.008c.875-.664,2.387-.812,2.492-2.148l.172-1.562,1.988-.2.539,1.516A1.7,1.7,0,0,0,71.8,43.375c1.086.133,2.285,1.082,3.293.242l1.277-.992,1.566,1.238c-.434.977-1.344,2.055-.562,3.113a7.968,7.968,0,0,1,.813,1.445,1.706,1.706,0,0,0,1.359,1l1.641.2.223,1.906-1.516.5a1.679,1.679,0,0,0-1.133,1.289,8.286,8.286,0,0,1-.453,1.559,1.685,1.685,0,0,0,.254,1.7L79.574,57.8l-1.215,1.484-1.477-.719a1.71,1.71,0,0,0-1.7.109Z"/>
                                    <path d="M77.09,51.047a6.854,6.854,0,1,0-6.84,7.473,6.885,6.885,0,0,0,6.84-7.473Zm-6.418,5.109a4.509,4.509,0,1,1-.4-9,4.533,4.533,0,0,1,.4,9Z"/>
                                </g>
                            </svg>
                        </div>
                        <h3>Flussi di Lavoro Complessi</h3>
                        <p>Processi multi-step attraverso diversi strumenti SaaS che rompono l'automazione semplice e
                            richiedono costante intervento umano.</p>
                    </div>
                    <div class="problem-card">
                        <div class="icon">
                            <svg xmlns="http://www.w3.org/2000/svg" width="72" height="72" viewBox="0 0 84.064 81.906" fill="currentColor">
                                <g transform="translate(-7.968 -9.047)">
                                    <path d="M39.156,27.3c-15.016,0-31.188-2.859-31.188-9.125S24.124,9.047,39.156,9.047s31.188,2.859,31.188,9.125S54.188,27.3,39.156,27.3Zm0-15.125c-18.156,0-28.062,3.969-28.062,6s9.906,6,28.062,6,28.062-3.969,28.062-6-9.906-6-28.062-6Z"/>
                                    <path d="M39.156,42.406c-15.016,0-31.188-2.859-31.188-9.125V18.156a1.562,1.562,0,1,1,3.125,0V33.281c0,2.031,9.906,6,28.062,6s28.062-3.969,28.062-6V18.156a1.563,1.563,0,0,1,3.125,0V33.281c0,6.266-16.156,9.125-31.188,9.125Z"/>
                                    <path d="M68.766,48.922A1.567,1.567,0,0,1,67.2,47.36v-14a1.562,1.562,0,0,1,3.125,0v14A1.567,1.567,0,0,1,68.766,48.922Z"/>
                                    <path d="M39.156,57.594c-15.016,0-31.188-2.859-31.188-9.125V33.36a1.562,1.562,0,1,1,3.125,0V48.469c0,2.031,9.906,6,28.062,6a104,104,0,0,0,13.75-.875,1.56,1.56,0,1,1,.406,3.094,107.284,107.284,0,0,1-14.156.906Z"/>
                                    <path d="M39.156,72.812c-15.016,0-31.188-2.844-31.188-9.109V48.578a1.562,1.562,0,1,1,3.125,0V63.7c0,2.031,9.906,5.984,28.062,5.984,3.141,0,6.266-.125,9.281-.391a1.56,1.56,0,1,1,.266,3.109c-3.094.266-6.312.391-9.531.391Z"/>
                                    <path d="M39.156,88.125C24.14,88.125,7.968,85.266,7.968,79V63.875a1.562,1.562,0,1,1,3.125,0V79c0,2.031,9.906,6,28.062,6A96.166,96.166,0,0,0,56.4,83.563a1.566,1.566,0,0,1,.578,3.078,98.244,98.244,0,0,1-17.828,1.5Z"/>
                                    <path d="M69.438,90.953A22.578,22.578,0,1,1,92.032,68.375,22.6,22.6,0,0,1,69.438,90.953Zm0-42.047A19.453,19.453,0,1,0,88.907,68.359,19.475,19.475,0,0,0,69.438,48.906Z"/>
                                    <path d="M69.438,72.344a1.567,1.567,0,0,1-1.562-1.562V54.063a1.563,1.563,0,0,1,3.125,0V70.782A1.567,1.567,0,0,1,69.438,72.344Z"/>
                                    <path d="M69.453,82.016a1.567,1.567,0,0,1-1.562-1.562V77.188a1.563,1.563,0,0,1,3.125,0v3.266A1.567,1.567,0,0,1,69.453,82.016Z"/>
                                </g>
                            </svg>
                        </div>
                        <h3>Sovraccarico di Dati</h3>
                        <p>Ricerca infinita, inserimento dati e analisi di mercato. Insight critici vengono persi in un
                            mare di copia-incolla e ricerche manuali.</p>
                    </div>
                    <div class="problem-card">
                        <div class="icon">
                            <svg xmlns="http://www.w3.org/2000/svg" width="72" height="72" viewBox="0 0 55.903 56" fill="currentColor">
                                <g transform="translate(-4 -4)">
                                    <g>
                                        <path d="M54.5,20.6l5.2-8.4V12c0-.1.1-.2.1-.3h0V5a1,1,0,0,0-1-1H5A.945.945,0,0,0,4,5v6.6H4a.367.367,0,0,0,.1.3v.2L22.3,41.8v3.5c.033.033,0,1.9,0,1.9V59a1.02,1.02,0,0,0,.5.8.551.551,0,0,0,.5.2.6.6,0,0,0,.4-.1l17.1-8.4a1.05,1.05,0,0,0,.5-.9V43.2a12.97,12.97,0,0,0,5.5,1.2A13.145,13.145,0,0,0,59.9,31.3,12.775,12.775,0,0,0,54.5,20.6ZM57.9,5.9v4.7H5.9V5.9ZM24.1,41,6.7,12.6H57.1l-4.3,7h-.1a6.117,6.117,0,0,0-1.2-.5c-.1,0-.1,0-.2-.1a4.951,4.951,0,0,0-1.3-.4c-.1,0-.2,0-.2-.1a5.853,5.853,0,0,0-1.3-.2h-.2c-.5,0-.9-.1-1.4-.1A13.145,13.145,0,0,0,33.8,31.3a9.7,9.7,0,0,0,.1,1.7,4.331,4.331,0,0,0,.1.5c.1.4.1.7.2,1.1.1.2.1.4.2.6l.3.9.3.6a5.937,5.937,0,0,0,.4.8,2.092,2.092,0,0,0,.4.6c.2.3.3.5.5.8.1.2.3.4.4.6.2.2.4.5.6.7l.5.5a4.349,4.349,0,0,0,.7.6c.2.2.4.3.6.5.2.1.3.3.5.4v3.2H24.3V41.7A1.165,1.165,0,0,0,24.1,41Zm.2,16.5V47.2H39.5V50ZM46.9,42.4a11.2,11.2,0,1,1,4.4-21.5,5.388,5.388,0,0,1,1.3.7,11.212,11.212,0,0,1-5.7,20.8Z"/>
                                        <path d="M51.7,26.4a.967.967,0,0,0-1.4,0l-3.5,3.5-3.5-3.5a.99.99,0,0,0-1.4,1.4l3.5,3.5L42,34.7a.967.967,0,0,0,0,1.4.967.967,0,0,0,1.4,0l3.5-3.5,3.5,3.5a.967.967,0,0,0,1.4,0,.967.967,0,0,0,0-1.4l-3.5-3.5,3.5-3.5A.946.946,0,0,0,51.7,26.4Z"/>
                                    </g>
                                </g>
                            </svg>
                        </div>
                        <h3>Funnel che Perdono</h3>
                        <p>Perdita di potenziali clienti a causa di qualificazione lenta dei lead, follow-up
                            inconsistenti e outreach generici.</p>
                    </div>
                </div>
            </div>
        </section>

        <section id="processo" class="container">
            <h2 class="section-title">Dal Problema al ROI in 3 Passaggi</h2>
            <p class="section-subtitle">Il nostro approccio studio è collaborativo e orientato ai risultati. Non
                consegniamo solo codice, consegniamo impatto aziendale misurabile.</p>
            <div class="process-grid">
                <div class="process-step">
                    <div class="number" data-number="01">01</div>
                    <h3>Scopri e Mappa</h3>
                    <p>Lavoriamo con te per comprendere profondamente la tua sfida unica, mappando ogni passaggio del
                        flusso di lavoro che automatizzeremo.</p>
                </div>
                <div class="process-step">
                    <div class="number" data-number="02">02</div>
                    <h3>Progetta e Implementa</h3>
                    <p>I nostri esperti costruiscono, testano e integrano il tuo agente IA personalizzato, assicurandosi
                        che funzioni perfettamente con i tuoi strumenti e team esistenti.</p>
                </div>
                <div class="process-step">
                    <div class="number" data-number="03">03</div>
                    <h3>Misura e Ottimizza</h3>
                    <p>Monitoriamo le performance e consegniamo report chiari sull'orario salvato, ricavi generati o
                        costi ridotti. Vero, tangibile ROI.</p>
                </div>
            </div>
        </section>

        <section id="soluzioni" class="solutions-section">
            <div class="container">
                <h2 class="section-title">Le Nostre Soluzioni IA</h2>
                <p class="section-subtitle">Scopri come i nostri agenti IA personalizzati stanno trasformando diversi
                    settori aziendali, automatizzando processi complessi e liberando tempo prezioso per attività
                    strategiche.</p>
                <div class="solutions-grid">
                    <div class="solution-card">
                        <img src="./images/automatizzazioni.png" alt="Gestione Appuntamenti" class="solution-image">
                        <div class="solution-content">
                            <h3>Gestione Appuntamenti N8N</h3>
                            <p class="description">Sistema automatizzato per la gestione completa degli appuntamenti: dalla
                                prenotazione alla conferma, promemoria automatici e riprogrammazione intelligente.
                                Integrazione seamless con calendari e CRM esistenti.</p>
                            <span class="time-saved">Risparmio: 15-20 ore/settimana</span>
                        </div>
                    </div>
                    <div class="solution-card">
                        <img src="./images/notizie.png" alt="News Sportive" class="solution-image">
                        <div class="solution-content">
                            <h3>Redazione News Sportive Automatizzata</h3>
                            <p class="description">IA che monitora risultati sportivi, statistiche e eventi in tempo reale,
                                generando automaticamente articoli coinvolgenti e aggiornati. Perfetto per siti web e
                                newsletter sportive.</p>
                            <span class="time-saved">Risparmio: 25-30 ore/settimana</span>
                        </div>
                    </div>
                    <div class="solution-card">
                        <img src="./images/avatar.png" alt="Avatar AR" class="solution-image">
                        <div class="solution-content">
                            <h3>Avatar AR Parlanti</h3>
                            <p class="description">Avatar in realtà aumentata che interagiscono con i clienti, fornendo
                                supporto personalizzato, presentazioni prodotto e assistenza 24/7. Rivoluziona l'esperienza
                                cliente e riduce i costi del supporto.</p>
                            <span class="time-saved">Risparmio: 40-50 ore/settimana</span>
                        </div>
                    </div>
                    <div class="solution-card">
                        <img src="./images/img02.png" alt="Libri Kindle" class="solution-image">
                        <div class="solution-content">
                            <h3>Generazione Automatizzata Libri Kindle</h3>
                            <p class="description">Sistema che ricerca, struttura e genera automaticamente contenuti per
                                libri Amazon Kindle. Dalla ricerca di mercato alla formattazione finale, tutto automatizzato
                                per massimizzare i profitti.</p>
                            <span class="time-saved">Risparmio: 60-80 ore/libro</span>
                        </div>
                    </div>
                    <div class="solution-card">
                        <img src="./images/kate.png" alt="Analisi Dati" class="solution-image">
                        <div class="solution-content">
                            <h3>Analisi Automatizzata Dati Aziendali</h3>
                            <p class="description">IA che monitora e analizza automaticamente i KPI aziendali, genera report
                                personalizzati e identifica trend e opportunità. Dashboard intelligenti con insights
                                predittivi per decisioni data-driven.</p>
                            <span class="time-saved">Risparmio: 20-25 ore/settimana</span>
                        </div>
                    </div>
                    <div class="solution-card">
                        <img src="./images/img02.png" alt="Assistente Virtuale" class="solution-image">
                        <div class="solution-content">
                            <h3>Assistente Virtuale Multicanale</h3>
                            <p class="description">Chatbot avanzato che gestisce automaticamente richieste clienti su
                                WhatsApp, email, social media e sito web. Risponde in linguaggio naturale, escalation
                                intelligente e integrazione CRM completa.</p>
                            <span class="time-saved">Risparmio: 35-40 ore/settimana</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="case-studies">
            <div class="container">
                <h2 class="section-title">Risultati, Non Report</h2>
                <p class="section-subtitle">Aiutiamo aziende ambiziose a raggiungere risultati eccezionali. Mentre i
                    dettagli del nostro lavoro sono confidenziali, l'impatto è chiaro.</p>
                <div class="case-study-grid">
                    <div class="case-study-card">
                        <div>
                            <p class="category">Automazione Generazione Lead</p>
                            <p class="metric">45%</p>
                            <p class="metric-label">Aumento di Lead Qualificati per Settimana</p>
                        </div>
                        <p class="testimonial">
                            "Questo ha completamente trasformato il nostro top-of-funnel. Il nostro team di vendite ora
                            dedica il tempo alla chiusura, non alla prospezione."
                            <span class="testimonial-author">&mdash; VP Vendite, Piattaforma SaaS B2B</span>
                        </p>
                    </div>
                    <div class="case-study-card">
                        <div>
                            <p class="category">Operazioni e Onboarding</p>
                            <p class="metric">90%</p>
                            <p class="metric-label">Riduzione delle Attività di Onboarding Manuali</p>
                        </div>
                        <p class="testimonial">
                            "Abbiamo recuperato oltre 20 ore a settimana del tempo del nostro operations manager ed
                            eliminato l'errore umano nel processo. Un vero game-changer."
                            <span class="testimonial-author">&mdash; COO, Azienda Tech Mid-Market</span>
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <section id="cta" class="container cta-section">
            <h2 class="section-title">Pronto a mettere le tue operazioni in autopilota?</h2>
            <p class="section-subtitle">Esploriamo come un agente IA personalizzato può risolvere il tuo collo di
                bottiglia operativo più pressante. La chiamata esplorativa iniziale è gratuita, e il potenziale ROI è
                immenso.</p>
            <a href="#" class="cta-button">Prenota la Tua Chiamata Esplorativa Gratuita</a>
        </section>

    </main>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-text">
                    <p>&copy; 2025 Proxy42 Inc. Tutti i diritti riservati.</p>
                </div>
                <div class="footer-logo">
                    <a href="https://proxy42.com/" target="_blank" rel="noopener noreferrer">
                        <img src="./images/logo_proxy.png" alt="Proxy Logo">
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Mobile Menu Toggle Functions
        function toggleMobileMenu() {
            const hamburger = document.querySelector('.hamburger');
            const mobileNav = document.querySelector('.nav-mobile');
            const overlay = document.querySelector('.nav-overlay');

            hamburger.classList.toggle('active');
            mobileNav.classList.toggle('active');
            overlay.classList.toggle('active');

            // Prevent body scroll when menu is open
            if (mobileNav.classList.contains('active')) {
                document.body.style.overflow = 'hidden';
            } else {
                document.body.style.overflow = 'auto';
            }
        }

        function closeMobileMenu() {
            const hamburger = document.querySelector('.hamburger');
            const mobileNav = document.querySelector('.nav-mobile');
            const overlay = document.querySelector('.nav-overlay');

            hamburger.classList.remove('active');
            mobileNav.classList.remove('active');
            overlay.classList.remove('active');
            document.body.style.overflow = 'auto';
        }

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', function (e) {
            const hamburger = document.querySelector('.hamburger');
            const mobileNav = document.querySelector('.nav-mobile');

            if (!hamburger.contains(e.target) && !mobileNav.contains(e.target)) {
                closeMobileMenu();
            }
        });

        // Close mobile menu on window resize
        window.addEventListener('resize', function () {
            if (window.innerWidth > 900) {
                closeMobileMenu();
            }
        });

        // Header scroll effect
        let lastScrollTop = 0;
        const header = document.querySelector('.header');

        window.addEventListener('scroll', function () {
            let scrollTop = window.pageYOffset || document.documentElement.scrollTop;

            // Add/remove scrolled class for styling
            if (scrollTop > 50) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }

            lastScrollTop = scrollTop;
        });
    </script>

</body>

</html>